# - Find Idfs<PERSON>
# Find the Linux Trace Toolkit - next generation with associated includes path.
# See http://idfs.org/
#
# This module accepts the following optional variables:
#    IDFS_PREFIX   = A hint on IDFSFS install path.
#
# This module defines the following variables:
#    IDFSFS_FOUND       = Was Idfs<PERSON> found or not?
#    IDFSFS_LIBRARIES   = The list of libraries to link to when using IdfsFS
#    IDFSFS_INCLUDE_DIR = The path to IdfsFS include directory
#
# On can set IDFS_PREFIX before using find_package(IdfsFS) and the
# module with use the PATH as a hint to find IdfsFS.
#
# The hint can be given on the command line too:
#   cmake -DIDFS_PREFIX=/DATA/ERIC/IdfsFS /path/to/source

if(IDFS_PREFIX)
  message(STATUS "FindIdfsFS: using PATH HINT: ${IDFS_PREFIX}")
  # Try to make the prefix override the normal paths
  find_path(IDFSFS_INCLUDE_DIR
    NAMES idfsfs/libidfsfs.h
    PATHS ${IDFS_PREFIX}
    PATH_SUFFIXES include
    NO_DEFAULT_PATH
    DOC "The IdfsFS include headers")

  find_path(IDFSFS_LIBRARY_DIR
    NAMES libidfsfs.so
    PATHS ${IDFS_PREFIX}
    PATH_SUFFIXES lib/${CMAKE_LIBRARY_ARCHITECTURE} lib lib64
    NO_DEFAULT_PATH
    DOC "The IdfsFS libraries")
endif(IDFS_PREFIX)

if (NOT IDFSFS_INCLUDE_DIR)
  find_path(IDFSFS_INCLUDE_DIR
    NAMES idfsfs/libidfsfs.h
    PATHS ${IDFS_PREFIX}
    PATH_SUFFIXES include
    DOC "The IdfsFS include headers")
endif (NOT IDFSFS_INCLUDE_DIR)

if (NOT IDFSFS_LIBRARY_DIR)
  find_path(IDFSFS_LIBRARY_DIR
    NAMES libidfsfs.so
    PATHS ${IDFS_PREFIX}
    PATH_SUFFIXES lib/${CMAKE_LIBRARY_ARCHITECTURE} lib lib64
    DOC "The IdfsFS libraries")
endif (NOT IDFSFS_LIBRARY_DIR)

find_library(IDFSFS_LIBRARY idfsfs PATHS ${IDFSFS_LIBRARY_DIR} NO_DEFAULT_PATH)
check_library_exists(idfsfs idfs_ll_lookup ${IDFSFS_LIBRARY_DIR} IDFS_FS)
if (NOT IDFS_FS)
  unset(IDFSFS_LIBRARY_DIR CACHE)
  unset(IDFSFS_INCLUDE_DIR CACHE)
else (NOT IDFS_FS)
  check_library_exists(idfsfs idfs_ll_mknod ${IDFSFS_LIBRARY_DIR} IDFS_FS_MKNOD)
  if(NOT IDFS_FS_MKNOD)
    message("Cannot find idfs_ll_mknod.  Disabling IDFS fsal mknod method")
    set(USE_FSAL_IDFS_MKNOD OFF)
  else(IDFS_FS_MKNOD)
    set(USE_FSAL_IDFS_MKNOD ON)
  endif(NOT IDFS_FS_MKNOD)
  check_library_exists(idfsfs idfs_ll_setlk ${IDFSFS_LIBRARY_DIR} IDFS_FS_SETLK)
  if(NOT IDFS_FS_SETLK)
    message("Cannot find idfs_ll_setlk.  Disabling IDFS fsal lock2 method")
    set(USE_FSAL_IDFS_SETLK OFF)
  else(IDFS_FS_SETLK)
    set(USE_FSAL_IDFS_SETLK ON)
  endif(NOT IDFS_FS_SETLK)
  check_library_exists(idfsfs idfs_ll_lookup_root ${IDFSFS_LIBRARY_DIR} IDFS_FS_LOOKUP_ROOT)
  if(NOT IDFS_FS_LOOKUP_ROOT)
    message("Cannot find idfs_ll_lookup_root. Working around it...")
    set(USE_FSAL_IDFS_LL_LOOKUP_ROOT OFF)
  else(NOT IDFS_FS_LOOKUP_ROOT)
    set(USE_FSAL_IDFS_LL_LOOKUP_ROOT ON)
  endif(NOT IDFS_FS_LOOKUP_ROOT)

  check_library_exists(idfsfs idfs_ll_delegation ${IDFSFS_LIBRARY_DIR} IDFS_FS_DELEGATION)
  if(NOT IDFS_FS_DELEGATION)
    message("Cannot find idfs_ll_delegation. Disabling support for delegations.")
    set(USE_FSAL_IDFS_LL_DELEGATION OFF)
  else(NOT IDFS_FS_DELEGATION)
    set(USE_FSAL_IDFS_LL_DELEGATION ON)
  endif(NOT IDFS_FS_DELEGATION)

  check_library_exists(idfsfs idfs_ll_sync_inode ${IDFSFS_LIBRARY_DIR} IDFS_FS_SYNC_INODE)
  if(NOT IDFS_FS_SYNC_INODE)
    message("Cannot find idfs_ll_sync_inode. SETATTR requests may be cached!")
    set(USE_FSAL_IDFS_LL_SYNC_INODE OFF)
  else(NOT IDFS_FS_SYNC_INODE)
    set(USE_FSAL_IDFS_LL_SYNC_INODE ON)
  endif(NOT IDFS_FS_SYNC_INODE)

  check_library_exists(idfsfs idfs_ll_fallocate ${IDFSFS_LIBRARY_DIR} IDFS_FALLOCATE)
  if(NOT IDFS_FALLOCATE)
    message("Cannot find idfs_ll_fallocate. No ALLOCATE or DEALLOCATE support!")
    set(USE_IDFS_FALLOCATE OFF)
  else(NOT IDFS_FALLOCATE)
    set(USE_IDFS_LL_FALLOCATE ON)
  endif(NOT IDFS_FALLOCATE)

  check_library_exists(idfsfs idfs_abort_conn ${IDFSFS_LIBRARY_DIR} IDFS_FS_ABORT_CONN)
  if(NOT IDFS_FS_ABORT_CONN)
	  message("Cannot find idfs_abort_conn. FSAL_IDFS will not leave session intact on clean shutdown.")
	  set(USE_FSAL_IDFS_ABORT_CONN OFF)
  else(NOT IDFS_FS_ABORT_CONN)
	  set(USE_FSAL_IDFS_ABORT_CONN ON)
  endif(NOT IDFS_FS_ABORT_CONN)

  check_library_exists(idfsfs idfs_start_reclaim ${IDFSFS_LIBRARY_DIR} IDFS_FS_RECLAIM_RESET)
  if(NOT IDFS_FS_RECLAIM_RESET)
	  message("Cannot find idfs_start_reclaim. FSAL_IDFS will not kill off old sessions.")
	  set(USE_FSAL_IDFS_RECLAIM_RESET OFF)
  else(NOT IDFS_FS_RECLAIM_RESET)
	  set(USE_FSAL_IDFS_RECLAIM_RESET ON)
  endif(NOT IDFS_FS_RECLAIM_RESET)

  check_library_exists(idfsfs idfs_select_filesystem ${IDFSFS_LIBRARY_DIR} IDFS_FS_GET_FS_CID)
  if(NOT IDFS_FS_GET_FS_CID)
	  message("Cannot find idfs_set_filesystem. FSAL_IDFS will only mount the default filesystem.")
	  set(USE_FSAL_IDFS_GET_FS_CID OFF)
  else(NOT IDFS_FS_GET_FS_CID)
	  set(USE_FSAL_IDFS_GET_FS_CID ON)
  endif(NOT IDFS_FS_GET_FS_CID)

  check_library_exists(idfsfs idfs_ll_register_callbacks ${IDFSFS_LIBRARY_DIR} IDFS_FS_REGISTER_CALLBACKS)
  if(NOT IDFS_FS_REGISTER_CALLBACKS)
	  message("Cannot find idfs_ll_register_callbacks. FSAL_IDFS will not respond to cache pressure requests from the DMS.")
	  set(USE_FSAL_IDFS_REGISTER_CALLBACKS OFF)
  else(NOT IDFS_FS_REGISTER_CALLBACKS)
	  set(USE_FSAL_IDFS_REGISTER_CALLBACKS ON)
  endif(NOT IDFS_FS_REGISTER_CALLBACKS)

  check_library_exists(idfsfs idfs_ll_lookup_vino ${IDFSFS_LIBRARY_DIR} IDFS_FS_LOOKUP_VINO)
  if(NOT IDFS_FS_LOOKUP_VINO)
	  message("Cannot find idfs_ll_lookup_vino. FSAL_IDFS will not be able to reliably look up snap inodes by handle.")
	  set(USE_FSAL_IDFS_LOOKUP_VINO OFF)
  else(NOT IDFS_FS_LOOKUP_VINO)
	  set(USE_FSAL_IDFS_LOOKUP_VINO ON)
  endif(NOT IDFS_FS_LOOKUP_VINO)

  set(CMAKE_REQUIRED_INCLUDES ${IDFSFS_INCLUDE_DIR})
  if (CMAKE_MAJOR_VERSION VERSION_EQUAL 3 AND CMAKE_MINOR_VERSION VERSION_GREATER 14)
    include(CheckSymbolExists)
  endif(CMAKE_MAJOR_VERSION VERSION_EQUAL 3 AND CMAKE_MINOR_VERSION VERSION_GREATER 14)
  check_symbol_exists(IDFS_STATX_INO "idfsfs/libidfsfs.h" IDFS_FS_IDFS_STATX)
  if(NOT IDFS_FS_IDFS_STATX)
    message("Cannot find IDFS_STATX_INO. Enabling backward compatibility for pre-idfs_statx APIs.")
    set(USE_FSAL_IDFS_STATX OFF)
  else(NOT IDFS_FS_IDFS_STATX)
    set(USE_FSAL_IDFS_STATX ON)
  endif(NOT IDFS_FS_IDFS_STATX)
  check_prototype_definition(idfs_ll_writev
    #"int64_t idfs_ll_writev(struct idfs_mount_info *cmount, struct Fh *fh,
    #                   const struct iovec *iov, int iovcnt, int64_t off);"
    "int64_t idfs_ll_writev(struct idfs_mount_info *cmount, struct Fh *fh,
                       struct iovec_t *iov, int64_t off);"
    "NULL"
    "unistd.h;idfsfs/libidfsfs.h"
    IDFS_FS_IDFS_ZEROCPY_WRITE)
  if(NOT IDFS_FS_IDFS_ZEROCPY_WRITE)
    message("Disable IDFS write zero copy APIs.")
    set(USE_FSAL_IDFS_ZEROCPY_WRITE OFF)
  else(NOT IDFS_FS_IDFS_ZEROCPY_WRITE)
    set(USE_FSAL_IDFS_ZEROCPY_WRITE ON)
  endif(NOT IDFS_FS_IDFS_ZEROCPY_WRITE)
  check_prototype_definition(idfs_ll_readv
    "int64_t idfs_ll_readv(struct idfs_mount_info *cmount, struct Fh *fh,
                      struct iovec_t *iov, int64_t off);"
    "NULL"
    "unistd.h;idfsfs/libidfsfs.h"
    IDFS_FS_IDFS_ZEROCPY_READ)
  if(NOT IDFS_FS_IDFS_ZEROCPY_READ)
    message("Disable IDFS read zero copy APIs.")
    set(USE_FSAL_IDFS_ZEROCPY_READ OFF)
  else(NOT IDFS_FS_IDFS_ZEROCPY_READ)
    set(USE_FSAL_IDFS_ZEROCPY_READ ON)
  endif(NOT IDFS_FS_IDFS_ZEROCPY_READ)
endif (NOT IDFS_FS)

set(IDFSFS_LIBRARIES ${IDFSFS_LIBRARY})
message(STATUS "Found idfsfs libraries: ${IDFSFS_LIBRARIES}")

# handle the QUIETLY and REQUIRED arguments and set PRELUDE_FOUND to TRUE if
# all listed variables are TRUE
include(FindPackageHandleStandardArgs)
FIND_PACKAGE_HANDLE_STANDARD_ARGS(IDFSFS
  REQUIRED_VARS IDFSFS_INCLUDE_DIR IDFSFS_LIBRARY_DIR)
# VERSION FPHSA options not handled by CMake version < 2.8.2)
#                                  VERSION_VAR)
mark_as_advanced(IDFSFS_INCLUDE_DIR)
mark_as_advanced(IDFSFS_LIBRARY_DIR)
mark_as_advanced(USE_FSAL_IDFS_MKNOD)
mark_as_advanced(USE_FSAL_IDFS_SETLK)
mark_as_advanced(USE_FSAL_IDFS_LL_LOOKUP_ROOT)
mark_as_advanced(USE_FSAL_IDFS_STATX)
