===================================================================
gnfs-kvsfs-config -- NFS Gnfs KVSFS Configuration File
===================================================================

.. program:: gnfs-kvsfs-config


SYNOPSIS
==========================================================

| /etc/gnfs/kvsfs.conf

DESCRIPTION
==========================================================

Gnfs install the following config file for KVSFS FSAL:
| /etc/gnfs/kvsfs.conf

This file lists KVSFS specific config options.

EXPORT { FSAL {} }
--------------------------------------------------------------------------------

Name(string, "KVSFS")
    Name of FSAL should always be KVSFS.

**kvsns_config(string default "/etc/kvsns/d/kvsns.ini")**
        the path to the kvsns.ini file. If not specified, default value is used

See also
==============================
:doc:`gnfs-log-config <gnfs-log-config>`\(8)
:doc:`gnfs-core-config <gnfs-core-config>`\(8)
:doc:`gnfs-export-config <gnfs-export-config>`\(8)
