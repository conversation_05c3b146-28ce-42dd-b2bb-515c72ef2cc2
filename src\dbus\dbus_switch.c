/**
 * @file  nfs_switch_dbus.c
 * @brief The switch  and support code.
 */
#include "config.h"
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <urcu-bp.h>
#ifdef LINUX
#include <mcheck.h>		/* For mtrace/muntrace */
#endif

#include "nfs_core.h"
#include "log.h"
#include "sal_functions.h"
#include "sal_data.h"
#include "idmapper.h"
#include "delayed_exec.h"
#include "export_mgr.h"
#include "pnfs_utils.h"
#include "fsal.h"
#include "netgroup_cache.h"
#ifdef USE_DBUS
#include "gsh_dbus.h"
#include "mdcache.h"
#endif
#include "conf_url.h"

#ifdef USE_DBUS
extern struct rpc_rdma_attr rpc_rdma_xa;
static bool proc_enable_write_loop(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_write_LOOPBACK ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_write_LOOPBACK = value;
	}
	else 
		return false;
	return true;	
}
static bool proc_enable_read_loop(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_read_LOOPBACK ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_read_LOOPBACK = value;
	}
	else 
		return false;
	return true;	
}
static bool proc_enable_access_loop(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_access_LOOPBACK ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_access_LOOPBACK = value;
	}
	else 
		return false;
	return true;	
}
static bool proc_enable_getattr_loop(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_getattr_LOOPBACK ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_getattr_LOOPBACK = value;
	}
	else 
		return false;
	return true;	
}

static bool proc_write_loopback_delay(char *type, DBusMessageIter *iter, uint64_t value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.write_loopback_delay);
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.write_loopback_delay = value;
	}
	else 
		return false;
	return true;	
}

static bool proc_read_loopback_delay(char *type, DBusMessageIter *iter, uint64_t value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.read_loopback_delay);
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.read_loopback_delay = value;
	}
	else 
		return false;
	return true;	
}

static bool proc_commit_delay(char *type, DBusMessageIter *iter, uint64_t value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.commit_delay);
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.commit_delay = value;
	}
	else 
		return false;
	return true;	
}

/*Enable_IO_Stats*/
static bool proc_enable_io_stats(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_IO_STATS ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_IO_STATS = value;
	}
	else 
		return false;
	return true;	
}

static bool proc_enable_write_zerocpy(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_write_ZEROCPY ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_write_ZEROCPY = value;
	}
	else 
		return false;
	return true;	
}

static bool proc_enable_read_zerocpy(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_read_ZEROCPY ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_read_ZEROCPY = value;
	}
	else 
		return false;
	return true;	
}

static bool proc_show_vip(char *type, DBusMessageIter *iter, bool value)
{
        if(!strcasecmp(type, "get")) {
		char value_str[1024];
                char *str_tmp = value_str;
                snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.show_vip ? "true" : "false");
                dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
        }
        else if(!strcasecmp(type, "set")) {
                nfs_param.core_param.show_vip= value;
                if (value) {
                        pthread_mutex_lock(&local_addrs.mutex);
                        memset(local_addrs.local_addr_lists, 0, IP_NUM_MAX*sizeof(struct local_addr_vip_t));
                        local_addrs.cur_pos = 0;
                        local_addrs.true_pos = 0;
                        pthread_mutex_unlock(&local_addrs.mutex);
                }
        }
        else
                return false;
        return true;
}

static bool proc_access_user_map(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.access_user_map ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.access_user_map = value;
	}
	else 
		return false;
	return true;	
}

static bool proc_mount_sys_tenant_v3(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.mount_sys_tenant_v3 ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.mount_sys_tenant_v3 = value;
	}
	else 
		return false;
	return true;	
}

static bool proc_mount_sys_tenant_v4(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.mount_sys_tenant_v4 ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.mount_sys_tenant_v4 = value;
	}
	else 
		return false;
	return true;	
}

static bool proc_time_threshold(char *type, DBusMessageIter *iter, uint64_t value)
{
	if(!strcasecmp(type, "get")) {
		dbus_uint64_t val = nfs_param.core_param.time_threshold;
		dbus_message_iter_append_basic(iter, DBUS_TYPE_UINT64, &val);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.time_threshold = value;
        }
        else
                return false;
        return true;
}

static bool proc_enable_to_get_inherit_acl(char *type, DBusMessageIter *iter, bool value){
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_to_get_inherit_acl ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_to_get_inherit_acl = value;
	}
	else 
		return false;
	return true;	



}
static bool proc_nfs_service_status(char *type, DBusMessageIter *iter, uint64_t value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.nfs_service_status);
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.nfs_service_status = value;
	}
	else 
		return false;
	return true;	

}

static bool proc_enable_check_nfs_service(char *type, DBusMessageIter *iter, bool value)
{
        if(!strcasecmp(type, "get")) {
                char value_str[1024];
                char *str_tmp = value_str;
                snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_check_nfs_service ? "true" : "false");
                dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
        }
        else if(!strcasecmp(type, "set")) {
                nfs_param.core_param.enable_check_nfs_service = value;
        }
        else
                return false;
        return true;
}

static bool proc_enable_to_get_inherit_acl_v4(char *type, DBusMessageIter *iter, bool value)
{
        if(!strcasecmp(type, "get")) {
                char value_str[1024];
                char *str_tmp = value_str;
                snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_to_get_inherit_acl_v4 ? "true" : "false");
                dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
        }
        else if(!strcasecmp(type, "set")) {
                nfs_param.core_param.enable_to_get_inherit_acl_v4 = value;
        }
        else
                return false;
        return true;
}

static bool proc_data_debug_log_write(char *type, DBusMessageIter *iter, bool value)
{
        if(!strcasecmp(type, "get")) {
                char value_str[1024];
                char *str_tmp = value_str;
                snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.data_debug_log_write ? "true" : "false");
                dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
        }
        else if(!strcasecmp(type, "set")) {
                nfs_param.core_param.data_debug_log_write = value;
        }
        else
                return false;
        return true;
}

static bool proc_data_debug_log_read(char *type, DBusMessageIter *iter, bool value)
{
        if(!strcasecmp(type, "get")) {
                char value_str[1024];
                char *str_tmp = value_str;
                snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.data_debug_log_read ? "true" : "false");
                dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
        }
        else if(!strcasecmp(type, "set")) {
                nfs_param.core_param.data_debug_log_read = value;
        }
        else
                return false;
        return true;
}

static bool proc_enable_nfs_service_status(char *type, DBusMessageIter *iter, bool value)
{
        if(!strcasecmp(type, "get")) {
                char value_str[1024];
                char *str_tmp = value_str;
                snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_nfs_service_status ? "true" : "false");
                dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
        }
        else if(!strcasecmp(type, "set")) {
                nfs_param.core_param.enable_nfs_service_status = value;
        }
        else
                return false;
        return true;
}

/*
static bool proc_enable_check_nfs_service_thread(char *type, DBusMessageIter *iter, bool value)
{
        if(!strcasecmp(type, "get")) {
                char value_str[1024];
                char *str_tmp = value_str;
                snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_check_nfs_service_thread ? "true" : "false");
                dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
        }
        else if(!strcasecmp(type, "set")) {
                nfs_param.core_param.enable_check_nfs_service_thread = value;
        }
        else
                return false;
        return true;
}
*/
static bool proc_loopback_for_size(char *type, DBusMessageIter *iter, uint64_t value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.loopback_for_size);
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.loopback_for_size = value;
	}
	else 
		return false;
	return true;	
}
static bool proc_enable_performance_opt(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.performance_OPT ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.performance_OPT = value;
	}
	else 
		return false;
	return true;	
}
static bool proc_loopback_for_write_scale(char *type, DBusMessageIter *iter, uint64_t value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.loopback_for_write_scale);
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.loopback_for_write_scale = value;
	}
	else 
		return false;
	return true;	
}

static bool proc_loopback_for_read_scale(char *type, DBusMessageIter *iter, uint64_t value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.loopback_for_read_scale);
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.loopback_for_read_scale = value;
	}
	else 
		return false;
	return true;	
}

static bool proc_enable_getattr_to_getacl(char *type, DBusMessageIter *iter, bool value)
{
        if(!strcasecmp(type, "get")) {
                char value_str[1024];
                char *str_tmp = value_str;
                snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_getattr_to_getacl ? "true" : "false");
                dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
        }
        else if(!strcasecmp(type, "set")) {
                nfs_param.core_param.enable_getattr_to_getacl = value;
        }
        else
                return false;
        return true;
}

static bool proc_enable_poc_noaccess(char *type, DBusMessageIter *iter, bool value)
{
        if(!strcasecmp(type, "get")) {
                char value_str[1024];
                char *str_tmp = value_str;
                snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_poc_noaccess ? "true" : "false");
                dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
        }
        else if(!strcasecmp(type, "set")) {
                nfs_param.core_param.enable_poc_noaccess = value;
        }
        else
                return false;
        return true;
}

static bool proc_enable_audit_read(char *type, DBusMessageIter *iter, bool value)
{
        if(!strcasecmp(type, "get")) {
                char value_str[1024];
                char *str_tmp = value_str;
                snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_audit_read ? "true" : "false");
                dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
        }
        else if(!strcasecmp(type, "set")) {
                nfs_param.core_param.enable_audit_read = value;
        }
        else
                return false;
        return true;
}

static bool proc_protocols(char *type, DBusMessageIter *iter, uint64_t value)
{
	if(!strcasecmp(type, "get")) {
		dbus_uint64_t val = nfs_param.core_param.core_options;
		dbus_message_iter_append_basic(iter, DBUS_TYPE_UINT64, &val);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.core_options = value;
	}
	else
		return false;
	return true;
}


static bool proc_enable_audit_write(char *type, DBusMessageIter *iter, bool value)
{
        if(!strcasecmp(type, "get")) {
                char value_str[1024];
                char *str_tmp = value_str;
                snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_audit_write ? "true" : "false");
                dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
        }
        else if(!strcasecmp(type, "set")) {
                nfs_param.core_param.enable_audit_write = value;
        }
        else
                return false;
        return true;
}
static bool proc_enable_flag_rdwr(char *type, DBusMessageIter *iter, bool value)
{
        if(!strcasecmp(type, "get")) {
                char value_str[1024];
                char *str_tmp = value_str;
                snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_flag_rdwr ? "true" : "false");
                dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
        }
        else if(!strcasecmp(type, "set")) {
                nfs_param.core_param.enable_flag_rdwr = value;
        }
        else
                return false;
        return true;
}
static bool proc_enable_probe_count(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_PROBECOUNT ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_PROBECOUNT = value;
	}
	else 
		return false;
	return true;
}

static bool proc_enable_probe_delay(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_PROBEDELAY ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_PROBEDELAY = value;
	}
	else
		return false;
	return true;
}


static bool proc_io_report_ism_time(char *type, DBusMessageIter *iter, uint64_t value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str, sizeof(value_str),"%ld", nfs_param.core_param.io_report_ism_time);
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.io_report_ism_time = value;
	}
	else 
		return false;
	return true;	
}

static bool proc_enable_qos(char *type, DBusMessageIter *iter, bool value)
{
        if(!strcasecmp(type, "get")) {
                char value_str[1024];
                char *str_tmp = value_str;
                snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_QOS ? "true" : "false");
                dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
        }
        else if(!strcasecmp(type, "set")) {
                nfs_param.core_param.enable_QOS = value;
        }
        else
                return false;
        return true;
}

static bool proc_qos_suspend_count_max(char *type, DBusMessageIter *iter, uint64_t value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.qos_suspend_count_max);
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.qos_suspend_count_max = value;
	}
	else 
		return false;
	return true;	

}

static bool proc_qos_suspend_count(char *type, DBusMessageIter *iter, uint64_t value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.qos_suspend_count);
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.qos_suspend_count = value;
	}
	else 
		return false;
	return true;	
}

//extern struct rpc_rdma_attr rpc_rdma_xa;
static bool proc_rdma_credit(char *type, DBusMessageIter *iter, uint64_t value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;

		snprintf(value_str,sizeof(value_str),"%d", rpc_rdma_xa.credits);
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		rpc_rdma_xa.credits = value;
	}
	else 
		return false;
	return true;	

}

static bool proc_enable_credicts_countable(char *type, DBusMessageIter *iter, bool value)
{
        if(!strcasecmp(type, "get")) {
                char value_str[1024];
                char *str_tmp = value_str;
                snprintf(value_str,sizeof(value_str),"%s", rpc_rdma_xa.enable_credicts_countable ? "true" : "false");
                dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
        }
        else if(!strcasecmp(type, "set")) {
                rpc_rdma_xa.enable_credicts_countable = value;
        }
        else
                return false;
        return true;
}

static bool proc_cred_queues_num(char *type, DBusMessageIter *iter, uint64_t value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;

		snprintf(value_str,sizeof(value_str),"%ld", rpc_rdma_xa.cred_queues_num);
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		rpc_rdma_xa.cred_queues_num = value;
	}
	else 
		return false;
	return true;	

}
static bool proc_readdir_count_v3(char *type, DBusMessageIter *iter, uint64_t value)
{
	if(!strcasecmp(type, "get")) {
		dbus_uint64_t val = nfs_param.core_param.readdir_count_v3;
		dbus_message_iter_append_basic(iter, DBUS_TYPE_UINT64, &val);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.readdir_count_v3 = value;
        }
        else
                return false;
        return true;
}


static bool proc_readdir_count_v4(char *type, DBusMessageIter *iter, uint64_t value)
{
	if(!strcasecmp(type, "get")) {
		dbus_uint64_t val = nfs_param.core_param.readdir_count_v4;
		dbus_message_iter_append_basic(iter, DBUS_TYPE_UINT64, &val);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.readdir_count_v4 = value;
        }
        else
                return false;
        return true;
}

static bool proc_get_group_list(char *type, DBusMessageIter *iter, bool value)
{
        if(!strcasecmp(type, "get")) {
                char value_str[1024];
                char *str_tmp = value_str;
                snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.get_group_list ? "true" : "false");
                dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
        }
        else if(!strcasecmp(type, "set")) {
                nfs_param.core_param.get_group_list = value;
        }
        else
                return false;
        return true;
}


static bool proc_get_local_group_list(char *type, DBusMessageIter *iter, bool value)
{
        if(!strcasecmp(type, "get")) {
                char value_str[1024];
                char *str_tmp = value_str;
                snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.get_local_group_list ? "true" : "false");
                dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
        }
        else if(!strcasecmp(type, "set")) {
                nfs_param.core_param.get_local_group_list = value;
        }
        else
                return false;
        return true;
}

static bool proc_enable_report_node(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_report_node ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_report_node = value;
	}
	else
		return false;
	return true;
}

static bool proc_enable_report_client(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_report_client ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_report_client = value;
	}
	else
		return false;
	return true;
}
static bool proc_enable_report_export(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_report_export ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_report_export = value;
	}
	else
		return false;
	return true;
}
static bool proc_enable_export_ops(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_export_ops_stats ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_export_ops_stats = value;
	}
	else
		return false;
	return true;
}
static bool proc_enable_export_io_stat(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_export_io_stat ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_export_io_stat = value;
	}
	else
		return false;
	return true;
}
static bool proc_enable_tenant_ops(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_tenant_ops_stats ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_tenant_ops_stats = value;
	}
	else
		return false;
	return true;
}


static bool proc_enable_report_tenant(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_report_tenant ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_report_tenant = value;
	}
	else
		return false;
	return true;
}

static bool proc_enable_tenant_client_ops(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_tenant_client_ops_stats ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_tenant_client_ops_stats = value;
	}
	else
		return false;
	return true;
}


static bool proc_enable_report_tenant_client(char *type, DBusMessageIter *iter, bool value)
{
	if(!strcasecmp(type, "get")) {
		char value_str[1024];
		char *str_tmp = value_str;
		snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_report_tenant_client ? "true" : "false");
		dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
	}
	else if(!strcasecmp(type, "set")) {
		nfs_param.core_param.enable_report_tenant_client = value;
	}
	else
		return false;
	return true;
}

static bool proc_enable_display_audit_nfs_log(char *type, DBusMessageIter *iter, bool value)
{
        if(!strcasecmp(type, "get")) {
                char value_str[1024];
                char *str_tmp = value_str;
                snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_display_audit_nfs_log ? "true" : "false");
                dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &str_tmp);
        }
        else if(!strcasecmp(type, "set")) {
                nfs_param.core_param.enable_display_audit_nfs_log = value;
        }
        else
                return false;
        return true;
}



/* #define SWITCH_COUNT 20 */
static char* switches[] = {
	
	"Enable_Write_Loopback",
	"Enable_Read_Loopback",
	"Enable_Access_Loopback",
	"Write_Loopback_Delay",
	"Read_Loopback_Delay",
	"Commit_Delay",
	"Enable_IO_Stats",
	"Enable_Write_ZeroCpy",
	"Enable_Read_ZeroCpy",
	"Show_Vip",
	"Access_User_Map",
	"Mount_Sys_Tenant_V3",
	"Mount_Sys_Tenant_V4",
	"Time_Threshold",
	"Enable_To_Get_Inherit_Acl",
	"Nfs_Service_Status",
	"Enable_Check_Nfs_Service",
	"Enable_To_Get_Inherit_Acl_V4",
	"Data_Debug_Log_Write",
	"Data_Debug_Log_Read",
	"Enable_Check_Nfs_Service_Thread",
	"Enable_Nfs_Service_Status",
	"Loopback_For_Size",
	"Performance_Opt",
	"Loopback_For_Read_Scale",
	"Loopback_For_Write_Scale",
	"Enable_Getattr_To_Getacl",
	"Enable_Poc_Noaccess",
	"Enable_Audit_Read",
	"Enable_Audit_Write",
	"Protocols",
	"enable_flag_rdwr",
	"Enable_Probe_Count",
	"Enable_Probe_Delay",
	"Io_Report_Ism_Time",
	"Enable_Qos",
	"Qos_Suspend_Count_Max",
	"Qos_Suspend_Count",
	"Readdir_Count_V3",
	"Readdir_Count_V4",
	"Get_Group_List",
	"Get_Local_Group_List",
	"Rdma_Credit",
	"Enable_Credicts_Countable",
	"Cred_Queues_Num",
	"Enable_Report_Node",
	"Enable_Report_Client",
	"Enable_Report_Export",
	"Enable_Export_Ops_Stats",
	"Enable_Export_Io_Stat",
	"Enable_Tenant_Ops_Stats",
	"Enable_Report_Tenant",
	"Enable_Tenant_Client_Ops_Stats",
	"Enable_Report_Tenant_Client",
	"Enable_Display_Audit_Nfs_Log"
};
static bool proc_print_all_switchs(DBusMessageIter *iter)
{
	int i = 0;
	DBusMessageIter ts_iter;
	char value_str[2048];
	char *str_tmp = NULL;
	memset(value_str, 0, sizeof(value_str));
	dbus_message_iter_open_container(iter,DBUS_TYPE_STRUCT, NULL,&ts_iter);
	int switch_cnt = sizeof(switches)/sizeof(char *);
	//for (i = 0; i < SWITCH_COUNT; ++i) {
	for (i = 0; i < switch_cnt; ++i) {
		dbus_message_iter_append_basic(&ts_iter, DBUS_TYPE_STRING, &switches[i]);

		if(!strcasecmp(switches[i], "Enable_Write_Loopback"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_write_LOOPBACK ? "true" : "false");
		
		else if (!strcasecmp(switches[i], "Enable_Read_Loopback"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_read_LOOPBACK ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Access_Loopback"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_access_LOOPBACK ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Getattr_Loopback"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_getattr_LOOPBACK ? "true" : "false");
		else if (!strcasecmp(switches[i], "Write_Loopback_Delay"))
			snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.write_loopback_delay);
		else if (!strcasecmp(switches[i], "Read_Loopback_Delay"))
			snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.read_loopback_delay);
		else if (!strcasecmp(switches[i], "Commit_Delay"))
			snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.commit_delay);
		else if (!strcasecmp(switches[i], "Enable_IO_Stats"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_IO_STATS ? "true" : "false");		
		else if (!strcasecmp(switches[i], "Enable_Write_ZeroCpy"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_write_ZEROCPY ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Read_ZeroCpy"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_read_ZEROCPY ? "true" : "false");
		else if (!strcasecmp(switches[i], "show_vip"))
                        snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.show_vip ? "true" : "false");
		else if (!strcasecmp(switches[i], "Access_User_Map"))
                        snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.access_user_map ? "true" : "false");
		else if (!strcasecmp(switches[i], "Mount_Sys_Tenant_V3"))
                        snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.mount_sys_tenant_v3 ? "true" : "false");
		else if (!strcasecmp(switches[i], "Mount_Sys_Tenant_V4"))
                        snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.mount_sys_tenant_v4 ? "true" : "false");
		else if (!strcasecmp(switches[i], "Time_Threshold"))
			snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.time_threshold);
		else if (!strcasecmp(switches[i], "Enable_To_Get_Inherit_Acl"))
                        snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_to_get_inherit_acl ? "true" : "false");
		else if (!strcasecmp(switches[i], "Nfs_Service_Status"))
			snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.nfs_service_status);
		else if (!strcasecmp(switches[i], "Enable_Check_Nfs_Service"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_check_nfs_service ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_To_Get_Inherit_Acl_V4"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_to_get_inherit_acl_v4 ? "true" : "false");
		else if (!strcasecmp(switches[i], "Data_Debug_Log_Write"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.data_debug_log_write ? "true" : "false");
		else if (!strcasecmp(switches[i], "Data_Debug_Log_Read"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.data_debug_log_read ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Check_Nfs_Service_Thread"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_check_nfs_service_thread ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Nfs_Service_Status"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_nfs_service_status ? "true" : "false");
		else if (!strcasecmp(switches[i], "Loopback_For_Size"))
			snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.loopback_for_size);
		else if (!strcasecmp(switches[i], "Performance_Opt"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.performance_OPT ? "true" : "false");
		else if (!strcasecmp(switches[i], "Loopback_For_Read_Scale"))
			snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.loopback_for_read_scale);
		else if (!strcasecmp(switches[i], "Loopback_For_Write_Scale"))
			snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.loopback_for_write_scale);
		else if (!strcasecmp(switches[i], "Enable_Getattr_To_Getacl"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_getattr_to_getacl ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Poc_Noaccess"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_poc_noaccess ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Audit_Read"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_audit_read ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Audit_Write"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_audit_write ? "true" : "false");
		else if (!strcasecmp(switches[i], "Protocols"))
			snprintf(value_str,sizeof(value_str),"%u", nfs_param.core_param.core_options);
		else if (!strcasecmp(switches[i], "Enable_Flag_Rdwr"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_flag_rdwr ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Probe_Count"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_PROBECOUNT ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Probe_Delay"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_PROBEDELAY ? "true" : "false");
		else if (!strcasecmp(switches[i], "Io_Report_Ism_Time"))
			snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.io_report_ism_time);
		else if (!strcasecmp(switches[i], "Enable_Qos"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_QOS ? "true" : "false");
		else if (!strcasecmp(switches[i], "Qos_Suspend_Count_Max"))
			snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.qos_suspend_count_max);
		else if (!strcasecmp(switches[i], "Qos_Suspend_Count"))
			snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.qos_suspend_count);
		else if (!strcasecmp(switches[i], "Readdir_Count_V3"))
			snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.readdir_count_v3);
		else if (!strcasecmp(switches[i], "Readdir_Count_V4"))
			snprintf(value_str,sizeof(value_str),"%ld", nfs_param.core_param.readdir_count_v4);
		else if (!strcasecmp(switches[i], "Get_Group_List"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.get_group_list ? "true" : "false");
		else if (!strcasecmp(switches[i], "Get_Local_Group_List"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.get_local_group_list ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Display_Audit_Nfs_Log"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_display_audit_nfs_log ? "true" : "false");
		else if (!strcasecmp(switches[i], "Rdma_Credit"))
			snprintf(value_str,sizeof(value_str),"%d", rpc_rdma_xa.credits);
		else if (!strcasecmp(switches[i], "Enable_Credicts_Countable"))
			snprintf(value_str,sizeof(value_str),"%s", rpc_rdma_xa.enable_credicts_countable ? "true" : "false");
		else if (!strcasecmp(switches[i], "Cred_Queues_Num"))
			snprintf(value_str,sizeof(value_str),"%ld", rpc_rdma_xa.cred_queues_num);
		else if (!strcasecmp(switches[i], "Enable_Report_Node"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_report_node ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Report_Client"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_report_client ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Report_Export"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_report_export ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Export_Ops_Stats"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_export_ops_stats ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Export_Io_Stat"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_export_io_stat ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Tenant_Ops_Stats"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_tenant_ops_stats ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Report_Tenant"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_report_tenant ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Tenant_Client_Ops_Stats"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_tenant_client_ops_stats ? "true" : "false");
		else if (!strcasecmp(switches[i], "Enable_Report_Tenant_Client"))
			snprintf(value_str,sizeof(value_str),"%s", nfs_param.core_param.enable_report_tenant_client ? "true" : "false");
		else
			snprintf(value_str,sizeof(value_str),"%s", "NA");
		str_tmp = value_str;
		dbus_message_iter_append_basic(&ts_iter, DBUS_TYPE_STRING, &str_tmp);
	}
	dbus_message_iter_close_container(iter, &ts_iter);

	return true;
}

static bool set_switch(DBusMessageIter *args,
				     DBusMessage *reply,
				     DBusError *error)
{
	bool ret = true;
	//char *errormsg = "OK";
	DBusMessageIter iter;
	char *switch_name;
	char *value;
	bool     val_bool = FALSE;
	uint64_t val_int64 = 0;
	//int      val_int = 0;
	
	dbus_message_iter_init_append(reply, &iter);
	if(!args)
		return false;
	if (dbus_message_iter_get_arg_type(args) != DBUS_TYPE_STRING)
		return false;
	dbus_message_iter_get_basic(args, &switch_name);

	if (!dbus_message_iter_next(args)
		   || dbus_message_iter_get_arg_type(args) != DBUS_TYPE_STRING
		    || dbus_message_iter_has_next(args)) {
		return false;
	}
	dbus_message_iter_get_basic(args, &value);	

	/* get switch value */
	if (!strcasecmp(value, "true")){
		val_bool = true;
	}
	else if (!strcasecmp(value, "false")){
		val_bool = false;
	}
	else{
		val_int64 = atol(value);
		//val_int = atoi(value);
	}

	if(!strcasecmp(switch_name, "Enable_Write_Loopback"))
		ret = proc_enable_write_loop("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Read_Loopback"))
		ret = proc_enable_read_loop("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Access_Loopback"))
		ret = proc_enable_access_loop("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Getattr_Loopback"))
		ret = proc_enable_getattr_loop("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Write_Loopback_Delay"))
		ret = proc_write_loopback_delay("set", &iter, val_int64);
	else if (!strcasecmp(switch_name, "Read_Loopback_Delay"))
		ret = proc_read_loopback_delay("set", &iter, val_int64);
	else if (!strcasecmp(switch_name, "Commit_Delay"))
		ret = proc_commit_delay("set", &iter, val_int64);
	else if (!strcasecmp(switch_name, "Enable_IO_Stats"))
		ret = proc_enable_io_stats("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Write_ZeroCpy"))
		ret = proc_enable_write_zerocpy("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Read_ZeroCpy"))
		ret = proc_enable_read_zerocpy("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "show_vip")) 
		ret = proc_show_vip("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Access_User_Map")) 
		ret = proc_access_user_map("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Mount_Sys_Tenant_V3")) 
		ret = proc_mount_sys_tenant_v3("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Mount_Sys_Tenant_V4")) 
		ret = proc_mount_sys_tenant_v4("set", &iter, val_bool);
	else if(!strcasecmp(switch_name, "Time_Threshold"))
		ret = proc_time_threshold("set", &iter, val_int64);
	else if (!strcasecmp(switch_name, "Enable_To_Get_Inherit_Acl")) 
		ret = proc_enable_to_get_inherit_acl("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Nfs_Service_Status"))
		ret = proc_nfs_service_status("set", &iter, val_int64);
	else if (!strcasecmp(switch_name, "Enable_Check_Nfs_Service"))
		ret = proc_enable_check_nfs_service("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_To_Get_Inherit_Acl_V4"))
		ret = proc_enable_to_get_inherit_acl_v4("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Data_Debug_Log_Read"))
		ret = proc_data_debug_log_read("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Data_Debug_Log_Write"))
		ret = proc_data_debug_log_write("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Nfs_Service_Status"))
		ret = proc_enable_nfs_service_status("set", &iter, val_bool);
	else if(!strcasecmp(switch_name, "Loopback_For_Size"))
		ret = proc_loopback_for_size("set", &iter, val_int64);
	else if(!strcasecmp(switch_name, "Loopback_For_Read_Scale"))
		ret = proc_loopback_for_read_scale("set", &iter, val_int64);
	else if(!strcasecmp(switch_name, "Loopback_For_Write_Scale"))
		ret = proc_loopback_for_write_scale("set", &iter, val_int64);
	else if (!strcasecmp(switch_name, "Performance_Opt"))
		ret = proc_enable_performance_opt("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Getattr_To_Getacl"))
		ret = proc_enable_getattr_to_getacl("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Poc_Noaccess"))
		ret = proc_enable_poc_noaccess("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Audit_Read"))
		ret = proc_enable_audit_read("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Audit_Write"))
		ret = proc_enable_audit_write("set", &iter, val_bool);
	else if(!strcasecmp(switch_name, "Protocols"))
		ret = proc_protocols("set", &iter, val_int64);
	else if (!strcasecmp(switch_name, "Enable_Flag_Rdwr"))
		ret = proc_enable_flag_rdwr("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Probe_Count"))
		ret = proc_enable_probe_count("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Probe_Delay"))
		ret = proc_enable_probe_delay("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Io_Report_Ism_Time"))
		ret = proc_io_report_ism_time("set", &iter, val_int64);
	else if (!strcasecmp(switch_name, "Enable_Qos"))
		ret = proc_enable_qos("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Qos_Suspend_Count_Max"))
		ret = proc_qos_suspend_count_max("set", &iter, val_int64);
	else if (!strcasecmp(switch_name, "Qos_Suspend_Count"))
		ret = proc_qos_suspend_count("set", &iter, val_int64);
	else if(!strcasecmp(switch_name, "Readdir_Count_V3"))
		ret = proc_readdir_count_v3("set", &iter, val_int64);
	else if(!strcasecmp(switch_name, "Readdir_Count_V4"))
		ret = proc_readdir_count_v4("set", &iter, val_int64);
	else if (!strcasecmp(switch_name, "Get_Group_List"))
		ret = proc_get_group_list("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Get_Local_Group_List"))
		ret = proc_get_local_group_list("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Rdma_Credit"))
		ret = proc_rdma_credit("set", &iter, val_int64);
	else if (!strcasecmp(switch_name, "Enable_Credicts_Countable"))
		ret = proc_enable_credicts_countable("set", &iter, val_bool);
	else if(!strcasecmp(switch_name, "Cred_Queues_Num"))
		ret = proc_cred_queues_num("set", &iter, val_int64);
	else if (!strcasecmp(switch_name, "Enable_Report_Node"))
		ret = proc_enable_report_node("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Report_Client"))
		ret = proc_enable_report_client("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Report_Export"))
		ret = proc_enable_report_export("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Export_Ops_Stats"))
		ret = proc_enable_export_ops("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Export_Io_Stat"))
		ret = proc_enable_export_io_stat("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Tenant_Ops_Stats"))
		ret = proc_enable_tenant_ops("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Report_Tenant"))
		ret = proc_enable_report_tenant("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Tenant_Client_Ops_Stats"))
		ret = proc_enable_tenant_client_ops("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Report_Tenant_Client"))
		ret = proc_enable_report_tenant_client("set", &iter, val_bool);
	else if (!strcasecmp(switch_name, "Enable_Display_Audit_Nfs_Log"))
		ret = proc_enable_display_audit_nfs_log("set", &iter, val_bool);
	else
		ret = false;

	//dbus_status_reply(&iter, success, errormsg);
	return ret;
}
static bool get_switch(DBusMessageIter *args,
				     DBusMessage *reply,
				     DBusError *error)
{
	bool ret = true;
	DBusMessageIter iter;
	DBusMessageIter ts_iter;
	char *switch_name;
	dbus_message_iter_init_append(reply, &iter);
	if(!args)
		return false;
	if (dbus_message_iter_get_arg_type(args) != DBUS_TYPE_STRING)
		return false;

	dbus_message_iter_get_basic(args, &switch_name);

	dbus_message_iter_open_container(&iter, DBUS_TYPE_STRUCT, NULL,&ts_iter);
	if(!strcasecmp(switch_name, "Enable_Write_Loopback"))
		ret = proc_enable_write_loop("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Read_Loopback"))
		ret = proc_enable_read_loop("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Access_Loopback"))
		ret = proc_enable_access_loop("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Getattr_Loopback"))
		ret = proc_enable_getattr_loop("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Write_Loopback_Delay"))
		ret = proc_write_loopback_delay("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Read_Loopback_Delay"))
		ret = proc_read_loopback_delay("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Commit_Delay"))
		ret = proc_commit_delay("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_IO_Stats"))
		ret = proc_enable_io_stats("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Write_ZeroCpy"))
		ret = proc_enable_write_zerocpy("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Read_ZeroCpy"))
		ret = proc_enable_read_zerocpy("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "show_vip"))
		ret = proc_show_vip("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Access_User_Map")) 
		ret = proc_access_user_map("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Mount_Sys_Tenant_V3")) 
		ret = proc_mount_sys_tenant_v3("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Mount_Sys_Tenant_V4")) 
		ret = proc_mount_sys_tenant_v4("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Time_Threshold")) 
		ret = proc_time_threshold("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_To_Get_Inherit_Acl")) 
		ret = proc_enable_to_get_inherit_acl("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Nfs_Service_Status"))
		ret = proc_nfs_service_status("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Check_Nfs_Service"))
		ret = proc_enable_check_nfs_service("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_To_Get_Inherit_Acl_V4"))
		ret = proc_enable_to_get_inherit_acl_v4("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Data_Debug_Log_Write"))
		ret = proc_data_debug_log_write("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Data_Debug_Log_Read"))
		ret = proc_data_debug_log_read("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Nfs_Service_Status"))
		ret = proc_enable_nfs_service_status("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Loopback_For_Size"))
		ret = proc_loopback_for_size("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Loopback_For_Write_Scale"))
		ret = proc_loopback_for_write_scale("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Loopback_For_Read_Scale"))
		ret = proc_loopback_for_read_scale("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Performance_Opt"))
		ret = proc_enable_performance_opt("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Getattr_To_Getacl"))
		ret = proc_enable_getattr_to_getacl("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Poc_Noaccess"))
		ret = proc_enable_poc_noaccess("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Audit_Read"))
		ret = proc_enable_audit_read("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Audit_Write"))
		ret = proc_enable_audit_write("get", &ts_iter, false);
	else if(!strcasecmp(switch_name, "Protocols"))
		ret = proc_protocols("get", &iter, false);
	else if (!strcasecmp(switch_name, "Enable_Flag_Rdwr"))
		ret = proc_enable_flag_rdwr("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Probe_Count"))
		ret = proc_enable_probe_count("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Probe_Delay"))
		ret = proc_enable_probe_delay("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Io_Report_Ism_Time"))
		ret = proc_io_report_ism_time("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Qos"))
		ret = proc_enable_qos("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Qos_Suspend_Count_Max"))
		ret = proc_qos_suspend_count_max("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Qos_Suspend_Count"))
		ret = proc_qos_suspend_count("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Readdir_Count_V3"))
		ret = proc_readdir_count_v3("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Readdir_Count_V4"))
		ret = proc_readdir_count_v4("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Get_Group_List"))
		ret = proc_get_group_list("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Get_Local_Group_List"))
		ret = proc_get_local_group_list("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Rdma_Credit"))
		ret = proc_rdma_credit("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Credicts_Countable"))
		ret = proc_enable_credicts_countable("get", &ts_iter, false);
	else if(!strcasecmp(switch_name, "Cred_Queues_Num"))
		ret = proc_cred_queues_num("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Report_Node"))
		ret = proc_enable_report_node("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Report_Client"))
		ret = proc_enable_report_client("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Report_Export"))
		ret = proc_enable_report_export("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Export_Ops_Stats"))
		ret = proc_enable_export_ops("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Export_Io_Stat"))
		ret = proc_enable_export_io_stat("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Tenant_Ops_Stats"))
		ret = proc_enable_tenant_ops("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Report_Tenant"))
		ret = proc_enable_report_tenant("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Tenant_Client_Ops_Stats"))
		ret = proc_enable_tenant_client_ops("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Report_Tenant_Client"))
		ret = proc_enable_report_tenant_client("get", &ts_iter, false);
	else if (!strcasecmp(switch_name, "Enable_Display_Audit_Nfs_Log"))
		ret = proc_enable_display_audit_nfs_log("get", &ts_iter, false);
	else
		ret = false;
	dbus_message_iter_close_container(&iter, &ts_iter);

	return ret;
}
static bool get_all_switchs(DBusMessageIter *args,
				     DBusMessage *reply,
				     DBusError *error)
{
	bool success = true;
	//char *errormsg = "OK";
	DBusMessageIter iter;
	dbus_message_iter_init_append(reply, &iter);

	success = proc_print_all_switchs(&iter);

	//dbus_status_reply(&iter, success, errormsg);
	return success;
}
//add by zhanghao at 2021.5.11 for manage switches  --end


// add by zhanghao for dynamic setting switch --begin
static struct gsh_dbus_method set_switchs = {
	.name = "SetSwitch",
	.method = set_switch,
	.args = {STRING_TYPE_ARG,
		 STRING_TYPE_ARG,
		 STATUS_REPLY,
		 END_ARG_LIST}
};
static struct gsh_dbus_method get_switchs = {
	.name = "GetSwitch",
	.method = get_switch,
	.args = {STRING_TYPE_ARG,
		 STATUS_REPLY,
		 END_ARG_LIST}
};
static struct gsh_dbus_method getall_switchs = {
	.name = "GetAllSwitch",
	.method = get_all_switchs,
	.args = { {.name = "switchnames",
		  	  .type = "a(ssss)",
		      .direction = "out"},
		 END_ARG_LIST}
};

static struct gsh_dbus_method *manage_switch_methods[] = {
	&set_switchs,
	&get_switchs,
	&getall_switchs,
	NULL
};

static struct gsh_dbus_interface manage_switch_interfaces = {
	.name = "org.gnfs.nfsd.switchmanage",
	.methods = manage_switch_methods
};

static struct gsh_dbus_interface *switch_interfaces[] = {
	&manage_switch_interfaces,
	NULL
};
void gsh_dbus_register_switch(void ){
	gsh_dbus_register_path("ManageSwitchs", switch_interfaces);
	return;
}
#endif
