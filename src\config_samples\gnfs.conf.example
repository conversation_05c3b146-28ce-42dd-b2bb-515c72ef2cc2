NFS_CORE_PARAM {
	Protocols = 3;
	#IDFS_Session_Timeout = 0;
	Enable_UDP = Mount;
	Enable_NFS_Stats = true;
	Enable_FULLV3_Stats = true;
	Enable_FULLV4_Stats = true;
	Enable_RPC_Stats = true;
	Enable_FSAL_Stats = true;
	Enable_THR_Stats = true;
	Enable_Memory_Stat = true;
	Enable_Write_ZeroCpy = true;
	Enable_Read_ZeroCpy = true;
}
RDMA {
	Enable_RDMA = true;
	NFS_RDMA_Port = 20049;	
}
NFSV4 {
	Grace_Period = 10;
}

# The libidfsfs client will aggressively cache information while it
# can, so there is little benefit to gnfs actively caching the same
# objects. Doing so can also hurt cache coherency. Here, we disable
# as much attribute and directory caching as we can.
MDCACHE {
	# Size the dirent cache down as small as possible.
	Dir_Chunk = 0;
}

## Configure an export for some file tree
EXPORT
{
	Export_Id = 76;
	Path = /;
	Pseudo = /;
	Access_Type = NONE;
	Squash = no_root_squash;
	FSAL {
		Name = IDFS;
	}
}
#LOG {
	## Default log level for all components
	#Default_Log_Level = WARN;
	## Configure per-component log levels.
	#Components {
		#FSAL = INFO;
		#NFS4 = EVENT;
	#}
	## Where to log
	#Facility {
		#name = FILE;
		#destination = "/var/log/gnfs.log";
		#enable = active;
	#}
#}
