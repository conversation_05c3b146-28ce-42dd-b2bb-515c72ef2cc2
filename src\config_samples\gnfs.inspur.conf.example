NFS_CORE_PARAM {
	#Protocols = 3,4,9P;
	IDFS_Session_Timeout = 0;A

	## stats switch
	Enable_NFS_Stats = true;
	Enable_FULLV3_Stats = true;
	Enable_FULLV4_Stats = true;
	Enable_RPC_Stats = true;
	Enable_FSAL_Stats = true;
	Enable_THR_Stats = true;
	Enable_Memory_Stat = true;
	#Enable_IO_Stats = true;
	
	## Performance switchs
	#Performance_Opt = true;
	#Enable_Root_Export = true;

	## zerocpy write/read switch
	Enable_Write_ZeroCpy = true;
	Enable_Read_ZeroCpy = true;
	#Test_Readv_iovs = 2;

	## loopback test and configure delay(us)
	#Enable_Write_Loopback = true;
	#Write_Loopback_Delay = 68; 
	#Enable_Read_Loopback = true;
	#Read_Loopback_Delay = 68; 
	#Commit_Delay = 20;

	## memory pool switch
	#Enable_Memory_Pool = true; 
        #Enable_Pool_Size=2000; 
}

## These are defaults for exports.  They can be overridden per-export.
#EXPORT_DEFAULTS {
	## Access type for clients.  Default is None, so some access must be
	## given either here or in the export itself.
	#Access_Type = RW;
#}

## Configure settings for the object handle cache
#MDCACHE {
	## The point at which object cache entries will start being reused.
	#Entries_HWMark = 100000;
#}

## Configure an export for some file tree
#EXPORT
#{
	## Export Id (mandatory, each EXPORT must have a unique Export_Id)
	#Export_Id = 12345;

	## Exported path (mandatory)
	#Path = /;

	## Pseudo Path (required for NFSv4 or if mount_path_pseudo = true)
	#Pseudo = /;

	## Restrict the protocols that may use this export.  This cannot allow
	## access that is denied in NFS_CORE_PARAM.
	#Protocols = 3,4;

	## Access type for clients.  Default is None, so some access must be
	## given. It can be here, in the EXPORT_DEFAULTS, or in a CLIENT block
	#Access_Type = RW;

	## Whether to squash various users.
	#Squash = no_root_squash;

	## Allowed security types for this export
	#Sectype = sys,krb5,krb5i,krb5p;

	## Exporting FSAL
	#FSAL {
	#	Name = IDFS;
	#}
#}
## Configure logging.  Default is to log to Syslog.  Basic logging can also be
## configured from the command line
#LOG {
	## Default log level for all components
	#Default_Log_Level = WARN;

	## Configure per-component log levels.
	#Components {
		#FSAL = INFO;
		#NFS4 = EVENT;
	#}

	## Where to log
	#Facility {
		#name = FILE;
		#destination = "/var/log/gnfs.log";
		#enable = active;
	#}
#}
