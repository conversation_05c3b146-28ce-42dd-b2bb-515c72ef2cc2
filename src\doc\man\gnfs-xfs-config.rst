===================================================================
gnfs-xfs-config -- NFS Gnfs XFS Configuration File
===================================================================

.. program:: gnfs-xfs-config


SYNOPSIS
==========================================================

    /etc/gnfs/xfs.conf

DESCRIPTION
==========================================================

Gnfs installs the config example for XFS FSAL:

    /etc/gnfs/xfs.conf

This file lists xfs specific config options.

EXPORT { FSAL {} }
--------------------------------------------------------------------------------

Name(string, "XFS")
    Name of FSAL should always be XFS.

XFS {}
--------------------------------------------------------------------------------
**link_support(bool, default true)**

**symlink_support(bool, default true)**

**cansettime(bool, default true)**

**maxread(uint64, range 512 to 64*1024*1024, default 64*1024*1024)**

**maxwrite(uint64, range 512 to 64*1024*1024, default 64*1024*1024)**

**umask(mode, range 0 to 0777, default 0)**

**auth_xdev_export(bool, default false)**

See also
==============================
:doc:`gnfs-log-config <gnfs-log-config>`\(8)
:doc:`gnfs-core-config <gnfs-core-config>`\(8)
:doc:`gnfs-export-config <gnfs-export-config>`\(8)
