FIND_PATH(READLIN<PERSON>_INCLUDE_DIR readline/readline.h)
FIND_LIBRARY(READLINE_LIBRARY NAMES readline)

IF (READLINE_INCLUDE_DIR AND READLINE_LIBRARY)
SET(READLINE_FOUND TRUE)
ENDIF (READ<PERSON><PERSON><PERSON>_INCLUDE_DIR AND READLINE_LIBRARY)

IF (READLINE_FOUND)
IF (NOT Readline_FIND_QUIETLY)
MESSAGE(STATUS "Found GNU readline: ${READLINE_LIBRARY}")
ENDIF (NOT Readline_FIND_QUIETLY)
ELSE (READLINE_FOUND)
IF (Readline_FIND_REQUIRED)
MESSAGE(FATAL_ERROR "Could not find GNU readline")
ENDIF (Readline_FIND_REQUIRED)
ENDIF (READLINE_FOUND)
