===================================================================
gnfs-dos-config -- NFS Gnfs DOS Configuration File
===================================================================

.. program:: gnfs-dos-config


SYNOPSIS
==========================================================

| /etc/gnfs/dos.conf

| /etc/gnfs/dos_bucket.conf

DESCRIPTION
==========================================================

Gnfs install two config examples for DOS FSAL:

| /etc/gnfs/dos.conf

| /etc/gnfs/dos_bucket.conf

This file lists DOS specific config options.

EXPORT { }
--------------------------------------------------------------------------------
DOS supports exporting both the buckets and filesystem.

.. Explain in detail about exporting bucket and filesystem

EXPORT { FSAL {} }
--------------------------------------------------------------------------------

Name(string, "DOS")
    Name of FSAL should always be DOS.

**User_Id(string, no default)**

**Access_Key(string, no default)**

**Secret_Access_Key(string, no default)**

DOS {}
--------------------------------------------------------------------------------
The following configuration variables customize the startup of the FSAL's
udsgw instance.

idfs_conf
    optional full-path to the Idfs configuration file (equivalent to passing
    "-c /path/to/idfs.conf" to any Idfs binary

name
    optional instance name (equivalent to passing "--name client.dos.foohost" to
    the udsgw binary);  the value provided here should be the same as the
    section name (sans brackets) of the udsgw facility in the Idfs
    configuration file (which must exist)

cluster
    optional cluster name (equivalent to passing "--cluster foo" to any Idfs
    binary);  use of a non-default value for cluster name is uncommon, but can
    be verified by examining the startup options of Idfs binaries

init_args
    additional argument strings which will be passed verbatim to the udsgw
    instance startup process as if they had been given on the udsgw command
    line provided for customization in uncommon setups

See also
==============================
:doc:`gnfs-log-config <gnfs-log-config>`\(8)
:doc:`gnfs-core-config <gnfs-core-config>`\(8)
:doc:`gnfs-export-config <gnfs-export-config>`\(8)
