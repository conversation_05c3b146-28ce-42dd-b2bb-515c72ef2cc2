
Test Find
{
   Product = FSAL POSIX.
   Command = ../scripts/non_reg_fsal_posix/compare_find_posix_posixdb.bash /etc
   Comment = FSAL posixdb find & unix find comparison.

        # test if we executed all the commands (10)
        # and the program ended well.
        Success TestOk
        {
          STDOUT =~ /OK/m
          AND
          STATUS == 0
        }
        
        # error
        Failure TestBAD
        {
          STDOUT =~ /BAD/m
          AND
          STATUS != 0
        }

        # fsal_posix_tool failure
        Failure TestFailure
        {
          STDERR =~ /Error/m
        }
        
}

