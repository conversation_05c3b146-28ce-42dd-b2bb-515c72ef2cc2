##
#
# This block controls the logging system
#
##

LOG {
	# The components block contains one or more logging components
	# and the setting to be used.
	components {
	  # The ALL component is special.  When set it defines the level
	  # for all components and overrides any other setting in this block.
	  # For convenience, many component names can be specified using two
	  # different values. One is the #define constant without the
	  # COMPONENT_ prefix, for example, HASHTABLE_CACHE. The other is
	  # the string that shows up in the actual log with blank replaced with
	  # underscore, for example HT_CACHE.
	  #
	  # Log levels are:
	  #
	  # NULL, FATAL, MAJ, CRIT, WARN, EVENT, INFO, DEBUG, MID_DEBUG,
	  # FULL_DEBUG
	  #
#	  ALL = FULL_DEBUG; # this will likely kill performance
#	  ALL = EVENT; # this is the default
#	  INIT = INFO;
#	  RPC = FULL_DEBUG;
#	  DBUS = DEBUG;

	}
	# Formatting of Log Messages
	# Each parameter is a field in the log message
	Format {
	  # date formats are: gnfs, local, ISO-8601, syslog, syslog_usec
	  #                   user_defined, none
	  # gnfs      time format %d/%m/%Y (DD/MM/YYYY)
	  #                          compatible with older Gnfs (pre 1.5)
	  # gnfs      date format %H:%M:%S (HH:MM:SS)
	  #                          compatible with older Gnfs (pre 1.5)
	  # local        date format is the local format as would show using %c
	  #                          in format string to strftime.
	  # local        time format %X (preferred local format)
	  # ISO-8601     date format %F (YYYY-MM-DD)
	  # ISO-8601     time format %X (preferred local format)
	  # syslog       date format %b %e (Mon MM)
	  # syslog       time format %X (preferred local format)
	  # syslog_usec  date format %F (YYYY-MM-DD)
	  # syslog_usec  time format T%H:%M:%S.%%06u%z (THH:MM:SS.UUUUUU+hhmm)
	  #                          date and time are separated by "T"
	  #                          instead of " ", +hhmm is the current UTC
	  #                          offset (can be - of course)
	  # none         date format no date
	  # none         time format no time
	  # user_defined date format specify a strftime format string
	  # user_defined time format specify a strftime format string
	  #                          you may specify the entire string for
	  #                          either time or date, and set the
	  #                          other to none
	  # date and time default to gnfs.
	  #
#	  date_format = gnfs;
#	  time_format = gnfs;
	  #
	  # If user_defined is set for date or time, these fields take a
	  # strftime type format.  These are examples. The default is empty.
	  # Note that you will need single or double quotes because it could
	  # have spaces and, in this particular example, it has a leading '%'
	  # which is not part of a regular token which must have a leading alphabetic.
	  #
#	  user_date_format = "%D";
#	  user_time_format = "%T";

	  # the following, if true, adds that field to the message
	  # these are the defaults
#	  HOSTNAME=true;
#	  PROGNAME=true;
#	  PID=true;
#	  EPOCH=true;
#	  CLIENTIP=false;
#	  THREAD_NAME=true;
#	  FUNCTION_NAME=true;
#	  COMPONENT=true;
#	  LEVEL=true;
#	  FILE_NAME= true;
#	  LINE_NUM= true;
	}

	# Facilities
	# these can be added or modified.
	# Three are defined by default, STDERR, STDOUT, and SYSLOG
	# if the '-L' option is used on the command line, a FILE
	# facility is created at startup with the option argument as
	# the output file.
#	facility {
	  # an arbitrary name.  If it matches an existing, the other
	  # parameters are used to modify it.
#	  name = FILE;
	  # Any higher level than this is not reported to this facility
#	  max_level = FULL_DEBUG;
	  # This can be stdout, stderr, or a file path
#	  destination = /var/log/gnfs/gnfs.log;
	  # facility state. Can be idle, active, or default
	  # An idle facility just sits there
	  # An active facility will accept log messages
	  # The default facility is special in that it cannot
	  # be removed or made idle.  You can switch another in its
	  # place however
#	  enable = default;
#	}

	# The wired default level is EVENT.  You change it here.
	# The default is set for any components not defined in the
	# components block.
	default_log_level = EVENT;
}
