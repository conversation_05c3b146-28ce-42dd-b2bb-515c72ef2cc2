#
# It is possible to use FSAL_IDFS to provide an NFS gateway to IdfsFS. The
# following sample config should be useful as a starting point for
# configuration. This basic configuration is suitable for a standalone NFS
# server, or an active/passive configuration managed by some sort of clustering
# software (e.g. pacemaker, docker, etc.).
#
# Note too that it is also possible to put a config file in UDS, and give
# gnfs a uds URL from which to fetch it. For instance, if the config
# file is stored in a UDS pool called "gnfs", in a namespace called
# "gnfs-namespace" with an object name of "gnfs-config":
#
# %url	uds://gnfs/gnfs-namespace/gnfs-config
#
# If we only export idfsfs (or DOS), store the configs and recovery data in
# UDS, and mandate NFSv4.1+ for access, we can avoid any sort of local
# storage, and gnfs can run as an unprivileged user (even inside a
# locked-down container).
#

NFS_CORE_PARAM
{
	# Gnfs can lift the NFS grace period early if NLM is disabled.
	Enable_NLM = false;

	# rquotad doesn't add any value here. IdfsFS doesn't support per-uid
	# quotas anyway.
	Enable_RQUOTA = false;

	# In this configuration, we're just exporting NFSv4. In practice, it's
	# best to use NFSv4.1+ to get the benefit of sessions.
	Protocols = 4;
}

NFSv4
{
	# Modern versions of libidfsfs have delegation support, though they
	# are not currently recommended in clustered configurations. They are
	# disabled by default but can be reenabled for singleton or
	# active/passive configurations.
	# Delegations = false;

	# One can use any recovery backend with this configuration, but being
	# able to store it in UDS is a nice feature that makes it easy to
	# migrate the daemon to another host.
	#
	# For a single-node or active/passive configuration, uds_ng driver
	# is preferred. For active/active clustered configurations, the
	# uds_cluster backend can be used instead. See the
	# gnfs-uds-grace manpage for more information.
	RecoveryBackend = uds_ng;

	# NFSv4.0 clients do not send a RECLAIM_COMPLETE, so we end up having
	# to wait out the entire grace period if there are any. Avoid them.
	Minor_Versions =  1,2;
}

# The libidfsfs client will aggressively cache information while it
# can, so there is little benefit to gnfs actively caching the same
# objects. Doing so can also hurt cache coherency. Here, we disable
# as much attribute and directory caching as we can.
MDCACHE {
	# Size the dirent cache down as small as possible.
	Dir_Chunk = 0;
}

EXPORT
{
	# Unique export ID number for this export
	Export_ID=100;

	# We're only interested in NFSv4 in this configuration
	Protocols = 4;

	# NFSv4 does not allow UDP transport
	Transports = TCP;

	#
	# Path into the idfsfs tree.
	#
	# Note that FSAL_IDFS does not support subtree checking, so there is
	# no way to validate that a filehandle presented by a client is
	# reachable via an exported subtree.
	#
	# For that reason, we just export "/" here.
	Path = /;

	#
	# The pseudoroot path. This is where the export will appear in the
	# NFS pseudoroot namespace.
	#
	Pseudo = /idfsfs_a/;

	# We want to be able to read and write
	Access_Type = RW;

	# Time out attribute cache entries immediately
	Attr_Expiration_Time = 0;

	# Enable read delegations? libidfsfs v13.0.1 and later allow the
	# idfs client to set a delegation. While it's possible to allow RW
	# delegations it's not recommended to enable them until gnfs
	# acquires CB_GETATTR support.
	#
	# Note too that delegations may not be safe in clustered
	# configurations, so it's probably best to just disable them until
	# this problem is resolved:
	#
	# http://tracker.idfs.com/issues/24802
	#
	# Delegations = R;

	# NFS servers usually decide to "squash" incoming requests from the
	# root user to a "nobody" user. It's possible to disable that, but for
	# now, we leave it enabled.
	# Squash = root;

	FSAL {
		# FSAL_IDFS export
		Name = IDFS;

		#
		# Idfs filesystems have a name string associated with them, and
		# modern versions of libidfsfs can mount them based on the
		# name. The default is to mount the default filesystem in the
		# cluster (usually the first one created).
		#
		# Filesystem = "idfsfs_a";

		#
		# Idfs clusters have their own authentication scheme (idfsx).
		# Gnfs acts as a idfsfs client. This is the client username
		# to use. This user will need to be created before running
		# gnfs.
		#
		# Typically idfs clients have a name like "client.foo". This
		# setting should not contain the "client." prefix.
		#
		# See:
		#
		# http://docs.idfs.com/docs/jewel/uds/operations/user-management/
		#
		# The default is to set this to NULL, which means that the
		# userid is set to the default in libidfsfs (which is
		# typically "admin").
		#
		# User_Id = "gnfs";

		#
		# Key to use for the session (if any). If not set, it uses the
		# normal search path for idfsx keyring files to find a key:
		#
		# Secret_Access_Key = "YOUR SECRET KEY HERE";
	}
}

# Config block for FSAL_IDFS
IDFS
{
	# Path to a idfs.conf file for this idfs cluster.
	# Idfs_Conf = /etc/idfs/idfs.conf;

	# User file-creation mask. These bits will be masked off from the unix
	# permissions on newly-created inodes.
	# umask = 0;
}

#
# This is the config block for the UDS RecoveryBackend. This is only
# used if you're storing the client recovery records in a UDS object.
#
UDS_KV
{
	# Path to a idfs.conf file for this cluster.
	# Idfs_Conf = /etc/idfs/idfs.conf;

	# The recoverybackend has its own idfs client. The default is to
	# let libidfsfs autogenerate the userid. Note that UDS_KV block does
	# not have a setting for Secret_Access_Key. A idfsx keyring file must
	# be used for authenticated access.
	# UserId = "gnfsrecov";

	# Pool ID of the idfs storage pool that contains the recovery objects.
	# The default is "gnfs".
	# pool = "gnfs";

	# Consider setting a unique nodeid for each running daemon here,
	# particularly if this daemon could end up migrating to a host with
	# a different hostname (i.e. if you're running an active/passive cluster
	# with uds_ng/uds_kv and/or a scale-out uds_cluster). The default
	# is to use the hostname of the node where gnfs is running.
	# nodeid = hostname.example.com
}

# Config block for uds:// URL access. It too uses its own client to access
# the object, separate from the FSAL_IDFS and UDS_KV client.
UDS_URLS
{
	# Path to a idfs.conf file for this cluster.
	# Idfs_Conf = /etc/idfs/idfs.conf;

	# UDS_URLS use their own idfs client too. Authenticated access
	# requires a idfsx keyring file.
	# UserId = "gnfsurls";

	# We can also have gnfs watch a UDS object for notifications, and
	# have it force a configuration reload when one comes in. Set this to
	# a valid uds:// URL to enable this feature.
	# watch_url = "uds://pool/namespace/object";
}
