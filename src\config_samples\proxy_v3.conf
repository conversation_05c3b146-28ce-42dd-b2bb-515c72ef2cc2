PROXY_V3 {
        # How many sockets to keep open in the connection pool.
        num_sockets = 64;
}

EXPORT
{
	# Export Id (mandatory, each EXPORT must have a unique Export_Id)
	Export_Id = 77;

	# Exported path (mandatory, path to export on the remote server)
	Path = /tmp;

	# Pseudo Path. This is required for NFS v4 *exporting*, so fill this in
        # even though this proxies v3 servers.
	Pseudo = /tmp_proxy;

	Access_Type = RW;

	Squash = no_root_squash;

	# Exporting FSAL
	FSAL {
		Name = PROXY_V3;
		Srv_Addr = 10.0.0.3;
	}
}
