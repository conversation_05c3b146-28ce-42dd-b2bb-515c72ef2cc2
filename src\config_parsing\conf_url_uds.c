/* ----------------------------------------------------------------------------
 * Copyright (C) 2017, Red Hat, Inc.
 * contributeur : <PERSON>  mben<PERSON>@redhat.com
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 * ---------------------------------------
 */

#include "conf_url.h"
#include "conf_url_uds.h"
#include <stdio.h>
#include <stdbool.h>
#include <regex.h>
#include "log.h"
#include "sal_functions.h"
#include <string.h>

static regex_t url_regex;
static uds_t cluster;
static bool initialized;
static uds_ioctx_t uds_watch_io_ctx;
static uint64_t uds_watch_cookie;
static char *uds_watch_oid;

static struct uds_url_parameter {
	/** Path to idfs.conf */
	char *idfs_conf;
	/** Userid (?) */
	char *userid;
	/** watch URL */
	char *watch_url;
} uds_url_param;

static struct config_item uds_url_params[] = {
	CONF_ITEM_PATH("idfs_conf", 1, MAXPATHLEN, NULL,
		       uds_url_parameter, idfs_conf),
	CONF_ITEM_STR("userid", 1, MAXPATHLEN, NULL,
		       uds_url_parameter, userid),
	CONF_ITEM_STR("watch_url", 1, MAXPATHLEN, NULL,
		       uds_url_parameter, watch_url),
	CONFIG_EOL
};

static void *uds_url_param_init(void *link_mem, void *self_struct)
{
	if (self_struct == NULL)
		return &uds_url_param;
	else
		return NULL;
}

struct config_block uds_url_param_blk = {
	.dbus_interface_name = "org.gnfs.nfsd.config.uds_urls",
	.blk_desc.name = "UDS_URLS",
	.blk_desc.type = CONFIG_BLOCK,
	.blk_desc.u.blk.init = uds_url_param_init,
	.blk_desc.u.blk.params = uds_url_params,
	.blk_desc.u.blk.commit = noop_conf_commit
};

static int uds_urls_set_param_from_conf(void *tree_node,
					  struct config_error_type *err_type)
{
	(void) load_config_from_node(tree_node,
				&uds_url_param_blk,
				NULL,
				true,
				err_type);

	if (!config_error_is_harmless(err_type)) {
		LogCrit(COMPONENT_INIT,
			"Error while parsing UDS_URLS config block");
		return -1;
	}

	LogFullDebug(COMPONENT_CONFIG,
		"%s parsed UDS_URLS block, have idfs_conf=%s "
		" userid=%s",
		__func__,
		uds_url_param.idfs_conf,
		uds_url_param.userid);

	return 0;
}


/* decompose UDS URL into (<pool>/(<namespace>/))object
 *
 *  verified to match each of the following:
 *
 *  #define URL1 "my_uds_object"
 *  #define URL2 "mypool_baby/myobject_baby"
 *  #define URL3 "mypool-baby/myobject-baby"
 */

#define UDS_URL_REGEX \
	"([-a-zA-Z0-9_&=.]+)/?([-a-zA-Z0-9_&=.]+)?/?([-a-zA-Z0-9_&=/.]+)?"

/** @brief url regex initializer
 */
static void init_url_regex(void)
{
	int r;

	r = regcomp(&url_regex, UDS_URL_REGEX, REG_EXTENDED);
	if (!!r) {
		LogFatal(COMPONENT_INIT,
			"Error initializing uds url regex");
	}
}

static void cu_uds_url_early_init(void)
{
	init_url_regex();
}

extern struct config_error_type err_type;

static int uds_url_client_setup(void)
{
	int ret;

	if (initialized)
		return 0;

	ret = uds_create(&cluster, uds_url_param.userid);
	if (ret < 0) {
		LogEvent(COMPONENT_CONFIG, "%s: Failed in uds_create",
			__func__);
		return ret;
	}

	ret = uds_conf_read_file(cluster, uds_url_param.idfs_conf);
	if (ret < 0) {
		LogEvent(COMPONENT_CLIENTID, "%s: Failed to read idfs_conf",
			__func__);
		uds_shutdown(cluster);
		return ret;
	}

	ret = uds_connect(cluster);
	if (ret < 0) {
		LogEvent(COMPONENT_CONFIG, "%s: Failed to connect to cluster",
			__func__);
		uds_shutdown(cluster);
		return ret;
	}

	init_url_regex();
	initialized = true;
	return 0;
}

static void cu_uds_url_init(void)
{
	int ret;
	void *node;

	node = config_GetBlockNode("UDS_URLS");
	if (node) {
		ret = uds_urls_set_param_from_conf(node, &err_type);
		if (ret < 0) {
			LogEvent(COMPONENT_CONFIG,
				"%s: Failed to parse UDS_URLS %d",
				__func__, ret);
		}
	} else {
		LogWarn(COMPONENT_CONFIG,
			"%s: UDS_URLS config block not found",
			__func__);
	}

	uds_url_client_setup();
}

static void cu_uds_url_shutdown(void)
{
	if (initialized) {
		uds_shutdown(cluster);
		regfree(&url_regex);
		initialized = false;
	}
}

static inline char *match_dup(regmatch_t *m, const char *in)
{
	char *s = NULL;

	if (m->rm_so >= 0) {
		int size;

		size = m->rm_eo - m->rm_so + 1;
		s = (char *)gsh_malloc(size);
		memcpy(s, in + m->rm_so, size - 1);
		s[size - 1] = '\0';
	}
	return s;
}

static int uds_url_parse(const char *url, char **pool, char **ns, char **obj)
{
	int ret;
	regmatch_t match[4];

	ret = regexec(&url_regex, url, 4, match, 0);
	if (likely(!ret)) {
		regmatch_t *m;
		char *x1, *x2, *x3;

		m = &(match[1]);
		x1 = match_dup(m, url);
		m = &(match[2]);
		x2 = match_dup(m, url);
		m = &(match[3]);
		x3 = match_dup(m, url);

		*pool = NULL;
		*ns = NULL;
		*obj = NULL;

		if (x1) {
			if (!x2) {
				/*
				 * object only
				 *
				 * FIXME: should we reject this case? I don't
				 * think there is such a thing as a default
				 * pool
				 */
				*obj = x1;
			} else {
				*pool = x1;
				if (!x3) {
					*obj = x2;
				} else {
					*ns = x2;
					*obj = x3;
				}
			}
		}
	} else if (ret == REG_NOMATCH) {
		LogWarn(COMPONENT_CONFIG,
			"%s: Failed to match %s as a config URL",
			__func__, url);
	} else {
		char ebuf[100];

		regerror(ret, &url_regex, ebuf, sizeof(ebuf));
		LogWarn(COMPONENT_CONFIG,
			"%s: Error in regexec: %s",
			__func__, ebuf);
	}
	return ret;
}

static int cu_uds_url_fetch(const char *url, FILE **f, char **fbuf)
{
	uds_ioctx_t io_ctx;

	char *pool_name = NULL;
	char *object_name = NULL;
	char *uds_ns = NULL;

	char *streambuf = NULL; /* not optional (buggy open_memstream) */
	FILE *stream = NULL;
	char buf[1024];

	size_t streamsz;
	uint64_t off1 = 0;
	int ret;

	if (!initialized) {
		cu_uds_url_init();
	}

	ret = uds_url_parse(url, &pool_name, &uds_ns, &object_name);
	if (ret)
		goto out;

	ret = uds_ioctx_create(cluster, pool_name, &io_ctx);
	if (ret < 0) {
		LogEvent(COMPONENT_CONFIG, "%s: Failed to create ioctx",
			__func__);
		cu_uds_url_shutdown();
		goto out;
	}
	uds_ioctx_set_namespace(io_ctx, uds_ns);

	do {
		int nread, wrt, nwrt;
		uint64_t off2 = 0;

		nread = ret = uds_read(io_ctx, object_name, buf, 1024, off1);
		if (ret < 0) {
			LogEvent(COMPONENT_CONFIG,
				"%s: Failed reading %s/%s %s", __func__,
				pool_name, object_name, strerror(ret));
			goto err;
		}
		off1 += nread;
		if (!stream) {
			streamsz = 1024;
			stream = open_memstream(&streambuf, &streamsz);
		}
		do {
			wrt = fwrite(buf+off2, 1, nread, stream);
			if (wrt > 0) {
				nwrt = MIN(nread, 1024);
				nread -= nwrt;
				off2 += nwrt;
			}
		} while (wrt > 0 && nread > 0);
	} while (ret > 0);

	if (likely(stream)) {
		/* rewind */
		fseek(stream, 0L, SEEK_SET);
		/* return--caller will release */
		*f = stream;
		*fbuf = streambuf;
		stream = NULL;
		streambuf = NULL;
	}

err:
	uds_ioctx_destroy(io_ctx);

out:
	if(stream)
		(void)fclose(stream);

	if(streambuf){
		/* Was allocated via open_memstream so use free NOT gsh_free. */
		free(streambuf);	
	}

	/* allocated or NULL */
	gsh_free(pool_name);
	gsh_free(uds_ns);
	gsh_free(object_name);

	return ret;
}

static struct gsh_url_provider uds_url_provider = {
	.name = "uds",
	.url_init = cu_uds_url_early_init,
	.url_shutdown = cu_uds_url_shutdown,
	.url_fetch = cu_uds_url_fetch
};

void conf_url_uds_pkginit(void)
{
	register_url_provider(&uds_url_provider);
}

static void uds_url_watchcb(void *arg, uint64_t notify_id, uint64_t handle,
			      uint64_t notifier_id, void *data, size_t data_len)
{
	int ret;

	/* ACK it to keep things moving */
	ret = uds_notify_ack(uds_watch_io_ctx, uds_watch_oid, notify_id,
				uds_watch_cookie, NULL, 0);
	if (ret < 0)
		LogEvent(COMPONENT_CONFIG, "uds_notify_ack failed: %d", ret);

	/* Send myself a SIGHUP */
	kill(getpid(), SIGHUP);
}

int uds_url_setup_watch(void)
{
	int ret;
	void *node;
	char *pool = NULL, *ns = NULL, *obj = NULL;
	char *url;

	/* No UDS_URLs block? Just return */
	node = config_GetBlockNode("UDS_URLS");
	if (!node)
		return 0;

	ret = uds_urls_set_param_from_conf(node, &err_type);
	if (ret < 0) {
		LogEvent(COMPONENT_CONFIG, "%s: Failed to parse UDS_URLS %d",
			 __func__, ret);
		return ret;
	}

	/* No watch parameter? Just return */
	if (uds_url_param.watch_url == NULL)
		return 0;

	if (strncmp(uds_url_param.watch_url, "uds://", 8)) {
		LogEvent(COMPONENT_CONFIG,
			 "watch_url doesn't start with uds://");
		return -1;
	}

	url = uds_url_param.watch_url + 8;

	/* Parse the URL */
	ret = uds_url_parse(url, &pool, &ns, &obj);
	if (ret)
		return ret;

	ret = uds_url_client_setup();
	if (ret)
		goto out;

	/* Set up an ioctx */
	ret = uds_ioctx_create(cluster, pool, &uds_watch_io_ctx);
	if (ret < 0) {
		LogEvent(COMPONENT_CONFIG, "%s: Failed to create ioctx",
			__func__);
		goto out;
	}
	uds_ioctx_set_namespace(uds_watch_io_ctx, ns);

	ret = uds_watch3(uds_watch_io_ctx, obj, &uds_watch_cookie,
			   uds_url_watchcb, NULL, 30, NULL);
	if (ret) {
		uds_ioctx_destroy(uds_watch_io_ctx);
		LogEvent(COMPONENT_CONFIG,
			 "Failed to set watch on UDS_URLS object: %d", ret);
	} else {
		uds_watch_oid = obj;
		obj = NULL;
	}
out:
	gsh_free(pool);
	gsh_free(ns);
	gsh_free(obj);

	return ret;
}

void uds_url_shutdown_watch(void)
{
	int ret;

	if (uds_watch_oid) {
		ret = uds_unwatch2(uds_watch_io_ctx, uds_watch_cookie);
		if (ret)
			LogEvent(COMPONENT_CONFIG,
				 "Failed to unwatch UDS_URLS object: %d",
				 ret);

		uds_ioctx_destroy(uds_watch_io_ctx);
		uds_watch_io_ctx = NULL;
		gsh_free(uds_watch_oid);
		uds_watch_oid = NULL;
		/* Leave teardown of client to the %url parser shutdown */
	}
}
