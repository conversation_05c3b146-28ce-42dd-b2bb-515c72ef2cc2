===================================================================
gnfs-lustre-config -- NFS Gnfs LUSTRE Configuration File
===================================================================

.. program:: gnfs-lustre-config


SYNOPSIS
==========================================================

| /etc/gnfs/lustre.conf

DESCRIPTION
==========================================================

Gnfs installa the config example for LUSTRE FSAL:
| /etc/gnfs/lustre.conf

This file lists LUSTRE specific config options.

EXPORT { FSAL {} }
--------------------------------------------------------------------------------

Name(string, "lustre")
    Name of FSAL should always be lustre.

**async_hsm_restore(bool, default true)**

All options of VFS export and module could be used for a FSAL_LUSTRE exporti and module.
:doc:`gnfs-vfs-config <gnfs-vfs-config>`\(8)

See also
==============================
:doc:`gnfs-vfs-config <gnfs-vfs-config>`\(8)
:doc:`gnfs-log-config <gnfs-log-config>`\(8)
:doc:`gnfs-core-config <gnfs-core-config>`\(8)
:doc:`gnfs-export-config <gnfs-export-config>`\(8)
