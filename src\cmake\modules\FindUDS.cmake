# - Find UDS
# This module accepts the following optional variables:
#    UDS_PREFIX   = A hint on UDS install path.
#
# This module defines the following variables:
#    UDS_FOUND       = Was UDS found or not?
#    UDS_LIBRARIES   = The list of libraries to link to when using UDS
#    UDS_INCLUDE_DIR = The path to UDS include directory
#
# On can set UDS_PREFIX before using find_package(UDS) and the
# module with use the PATH as a hint to find UDS.
#
# The hint can be given on the command line too:
#   cmake -DUDS_PREFIX=/DATA/ERIC/UDS /path/to/source

if(UDS_PREFIX)
  message(STATUS "FindUDS: using PATH HINT: ${UDS_PREFIX}")
  # Try to make the prefix override the normal paths
  find_path(UDS_INCLUDE_DIR
    NAMES uds/libuds.h
    PATHS ${UDS_PREFIX}
    PATH_SUFFIXES include
    NO_DEFAULT_PATH
    DOC "The UDS include headers")

  find_path(UDS_LIBRARY_DIR
    NAMES libuds.so
    PATHS ${UDS_PREFIX}
    PATH_SUFFIXES lib/${CMAKE_LIBRARY_ARCHITECTURE} lib lib64
    NO_DEFAULT_PATH
    DOC "The UDS libraries")
endif(UDS_PREFIX)


if (NOT UDS_INCLUDE_DIR)
  find_path(UDS_INCLUDE_DIR
    NAMES uds/libuds.h
    PATHS ${UDS_PREFIX}
    PATH_SUFFIXES include
    DOC "The UDS include headers")
endif (NOT UDS_INCLUDE_DIR)

if (NOT UDS_LIBRARY_DIR)
  find_path(UDS_LIBRARY_DIR
    NAMES libuds.so
    PATHS ${UDS_PREFIX}
    PATH_SUFFIXES lib/${CMAKE_LIBRARY_ARCHITECTURE} lib lib64
    DOC "The UDS libraries")
endif (NOT UDS_LIBRARY_DIR)

find_library(UDS_LIBRARY uds PATHS ${UDS_LIBRARY_DIR} NO_DEFAULT_PATH)
check_library_exists(uds uds_read_op_omap_get_vals2 ${UDS_LIBRARY_DIR} UDSLIB)
if (NOT UDSLIB)
  unset(UDS_LIBRARY_DIR CACHE)
  unset(UDS_INCLUDE_DIR CACHE)
endif (NOT UDSLIB)

set(UDS_LIBRARIES ${UDS_LIBRARY})
message(STATUS "Found uds libraries: ${UDS_LIBRARIES}")

# handle the QUIETLY and REQUIRED arguments and set PRELUDE_FOUND to TRUE if
# all listed variables are TRUE
include(FindPackageHandleStandardArgs)
FIND_PACKAGE_HANDLE_STANDARD_ARGS(UDS
  REQUIRED_VARS UDS_INCLUDE_DIR UDS_LIBRARY_DIR
  )
# VERSION FPHSA options not handled by CMake version < 2.8.2)
#                                  VERSION_VAR)

mark_as_advanced(UDS_INCLUDE_DIR)
mark_as_advanced(UDS_LIBRARY_DIR)
