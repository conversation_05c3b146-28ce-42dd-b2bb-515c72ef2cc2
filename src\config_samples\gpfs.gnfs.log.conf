LOG
{
         Default_log_level = EVENT;
         Facility {
                  name = FILE;
                  destination = /var/log/gnfs/gnfs.log;
                  max_level = FULL_DEBUG;
                  headers = all;
                  enable = idle;
         }
         Format {
                date_format = ISO-8601;
                time_format = ISO-8601;
                EPOCH = TRUE;
                CLIENTIP = FALSE;
                HOSTNAME = TRUE;
                PID = TRUE;
                THREAD_NAME = TRUE;
                FILE_NAME = FALSE;
                LINE_NUM = FALSE;
                FUNCTION_NAME = TRUE;
                COMPONENT = TRUE;
                LEVEL = TRUE;
         }
         Components {
               # Eg. COMPONENT = LEVEL;
               ALL = EVENT;
         }
}
