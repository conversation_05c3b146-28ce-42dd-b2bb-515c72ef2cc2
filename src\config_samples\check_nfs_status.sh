#! /bin/bash

key="Total"

declare -i log_clear_flag=0
log_path="/var/log/gnfs"
tool="/usr/bin/gnfs_stats"
log_file="/var/log/gnfs/check_gnfs_stuck.log"


function get_total_requests()
{
  ${tool} fsal idfs|grep ${key}| awk '{print $2" "$3}'
}

function get_dbus_return()
{
  ${tool} fsal idfs|grep "No stats available for display"
}

function check_exists_and_get_requests()
{

    total=$(get_total_requests)
    if [ -z "${total}" ];then
      # gnfs is restart now
      dbus_return=$(get_dbus_return)
      if [ -z "${dbus_return}" ];then
                echo "-1 -1"
          else
                echo "-2 -2"
          fi
    else
      echo "${total}"
    fi
}


function check_nfs_ops()
{
  #while [ 1 ];do
    cur_date=`date '+%Y-%m-%d %H:%M:%S'`
    echo "`date '+%Y-%m-%d %H:%M:%S'`:start check_nfs_ops" >> ${log_file}

    declare -i rr_arr
    declare -i hr_arr
    # get total_requests
    for((i =1;i<=3;i++))
    do

      results_array=$(check_exists_and_get_requests)
      #echo "====== results_array = ${results_array} ======"
      eval $(echo ${results_array} | awk '{printf("rr=%s;hr=%s"),$1,$2}')
      #echo "rr : ${rr}, hr : ${hr}"
      rr_arr[${i}]="${rr}"
      hr_arr[${i}]="${hr}"
      echo "`date '+%Y-%m-%d %H:%M:%S'`:receive_requests[${i}]=${rr},handle_requests[${i}]=${hr}" >> ${log_file}

      # check_exists_and_get_requests return -1, get_total_requests return null
      if [ ${rr} -eq -1 ];then
          echo "`date '+%Y-%m-%d %H:%M:%S'`:gnfs is request stuck. check_nfs_ops end, return:105" >> ${log_file}
          return 105

      fi
      # check_exists_and_get_requests return -2, check_program_exist return 1
      if [ ${rr} -eq -2 ];then
          echo "`date '+%Y-%m-%d %H:%M:%S'`:gnfs is no idfs_ops. check_nfs_ops end, return:1" >> ${log_file}
          return 1
      fi

      # the second time fetch
      if [ ${i} -eq 2 ];then
        # receive_request compare
        if [ ${rr_arr[2]} -ne ${hr_arr[2]} -a ${hr_arr[1]} -eq ${hr_arr[2]} ];then
          gnfs_pid=$(pidof gnfs.nfsd)
          if [ -n "${gnfs_pid}" ];then
            #timeout 30 pstack ${gnfs_pid} > ${log_path}/pstack-${gnfs_pid}-1.txt
            #            ps -ef|grep /usr/bin/gdb |grep /proc/${gnfs_pid}/|awk '{print $2}'|xargs -i kill -9 {} >> ${log_file}
	    timeout 5 gnfs_stats fsal idfs |tee -a >> ${log_file}
          fi
          echo "`date '+%Y-%m-%d %H:%M:%S'`:receive request stuck, second check gnfs" >> ${log_file}
        else
          echo "`date '+%Y-%m-%d %H:%M:%S'`:check_nfs_ops end, return:1" >> ${log_file}
          return 1
          # break
        fi
      fi

      if [ ${i} -ne 3 ];then
        sleep 60
      else
        # receive_request compare
        if [ ${rr_arr[3]} -ne ${hr_arr[3]} -a ${hr_arr[2]} -eq ${hr_arr[3]} ];then
          gnfs_pid=$(pidof gnfs.nfsd)
          if [ -n "${gnfs_pid}" ];then

            #timeout 30 pstack ${gnfs_pid} > ${log_path}/pstack-${gnfs_pid}-2.txt
            #            ps -ef|grep /usr/bin/gdb |grep /proc/${gnfs_pid}/|awk '{print $2}'|xargs -i kill -9 {} >> ${log_file}
	    timeout 5 gnfs_stats fsal idfs |tee -a >> ${log_file}
          fi
          echo "`date '+%Y-%m-%d %H:%M:%S'`:receive request stuck, need restart gnfs. check_nfs_ops end, return:103" >> ${log_file}
          return 103
          #break
        fi
        echo "`date '+%Y-%m-%d %H:%M:%S'`:check_nfs_ops end, return:1" >> ${log_file}
        return 1
        #break
      fi
    done
    echo "`date '+%Y-%m-%d %H:%M:%S'`:check_nfs_ops end" >> ${log_file}

    # clear log file
    #clear_logfile_timed

    # sleep 3s 
    sleep 60
  #done
}

function check_nfs_state()
{
        nfs_ok="Export list"
        cmd="showmount -e"

        cur_date=`date '+%Y-%m-%d %H:%M:%S'`
        echo "`date '+%Y-%m-%d %H:%M:%S'`:start check_nfs_state" >> ${log_file}


        result=`$cmd`
        rt=$(echo $result |grep "${nfs_ok}")
        echo "`date '+%Y-%m-%d %H:%M:%S'`: showmount -e:$rt" >> ${log_file}

        # ckesk is normal
        if [[ "$rt" != "" ]]
        then
            echo "`date '+%Y-%m-%d %H:%M:%S'`:check_nfs_state end, return:1" >> ${log_file}
            return 1
        else
            # if gnfs is inactive, the nfspid is null
            nfspid=`ps -ef|grep gnfs.nfsd |grep -v grep |awk '{print $2}'`
            echo "`date '+%Y-%m-%d %H:%M:%S'`:gnfspid: $nfspid" >> ${log_file}
            if [ -n "$nfspid" ]
            then
                # process of gnfs is exists
                pspath="/var/log/gnfs"

		#timeout 30 pstack $nfspid > ${log_path}/pstack-$nfspid-1.txt
                #        ps -ef|grep /usr/bin/gdb |grep /proc/$nfspid/|awk '{print $2}'|xargs -i kill -9 {} >> ${log_file}
		timeout 5 gnfs_stats fsal idfs |tee -a >> ${log_file}
                echo "`date '+%Y-%m-%d %H:%M:%S'`:first print stack: $pspath$nfspid-01.txt, need restart gnfs, check_nfs_state end, return:101" >> ${log_file}
                return 101

            else

                echo "`date '+%Y-%m-%d %H:%M:%S'`:gnfs is inactive. check_nfs_state end, return:102" >> ${log_file}
                return 102
            fi
        fi
        echo "============ check_nfs_state end ============" >> ${log_file}
}

function check_nfs_netstat()
{
        curtime=$(date +%Y%m%d-%H%M%S)
        #echo $curtime

        thresh=${1:-"10000"}
        slptime=${2:-"60"}

        cur_date=`date '+%Y-%m-%d %H:%M:%S'`
        echo "`date '+%Y-%m-%d %H:%M:%S'`:start check_nfs_netstat " >> ${log_file}

        netstatinfo=`netstat -tunpalx|grep gnfs|grep ':2049'|awk '$2>="'${thresh}'"'`

        echo "`date '+%Y-%m-%d %H:%M:%S'`:thresh=${thresh},slptime=${slptime}" >> ${log_file}
        # no number here is greater than the $thresh, exit directly
        [ "${netstatinfo}"x = ""x ] && { echo "`date '+%Y-%m-%d %H:%M:%S'`:check_nfs_netstat end, return:1" >> ${log_file}; return 1; }


        echo "`date '+%Y-%m-%d %H:%M:%S'`:${netstatinfo}" >> ${log_file}
        clientlist=`echo "${netstatinfo}"|awk '{print $5}'|cut -d : -f 1`


        declare -A conncnt
        declare -A increcnt
        for i in ${clientlist};
        do
          cnt=`echo "${netstatinfo}"|grep -w $i|awk '{print $2}'`
          #echo ${cnt}
          conncnt[${i}]=${cnt}
          increcnt[${i}]=0
          echo "${curtime},$i,${conncnt[${i}]}" >> ${log_file}
        done

        for ((i=0;i<2;i++));
        do
          sleep ${slptime}
          curtime=$(date +%Y%m%d-%H%M%S)

          netstatinfonew=`netstat -tunplxa|grep gnfs|grep ':2049'|awk '$2>="'${thresh}'"'`
          echo "`date '+%Y-%m-%d %H:%M:%S'`:${netstatinfonew}" >> ${log_file}
          for j in ${clientlist};
          do
            cnt=`echo "${netstatinfonew}"|grep -w $j|awk '{print $2}'`
            [ "${cnt}"x = ""x ] && { cnt=0; }
            echo "`date '+%Y-%m-%d %H:%M:%S'`:ttt=$cnt,${conncnt[${j}]}" >> ${log_file}
            if [ ${cnt} -ge ${conncnt[${j}]} ];then
                 let increcnt[${j}]++
                         conncnt[${j}]=${cnt}
            fi
            echo "`date '+%Y-%m-%d %H:%M:%S'`:${curtime},$j,$cnt" >> ${log_file}
          done
        done

        curtime=$(date +%Y%m%d-%H%M%S)
        for i in ${clientlist};
        do

          if [ ${increcnt[${i}]} -ge 2 ];then

             echo "`date '+%Y-%m-%d %H:%M:%S'`: need restart gnfs, check_nfs_netstat end, return:1" >> ${log_file}
             return 106

          fi
        done

        echo "`date '+%Y-%m-%d %H:%M:%S'`:check_nfs_netstat end, return:1" >> ${log_file}
        return 1

}

function check_all()
{
#while [ 1 ];do

        check_nfs_state
        #echo "check_nfs_state return $?"
        value1=$?
        if [ $value1 -eq 1 ];then
                check_nfs_ops
                value2=$?
		echo "check_nfs_ops return:$value2"
                #if [ $value2 -eq 1 ];then
                #        check_nfs_netstat 3000 30
                #        value3=$?
                        #echo $value3
                #        echo "check_nfs_netstat return:$value3"
                #else
                #        echo "check_nfs_ops return:$value2"
                #fi
        else
                echo "check_nfs_state return:$value1"
        fi

        #sleep 30
#done
}

function main()
{
while [ 1 ];do

  check_program_exist
  if [ $? -eq 1 ];then
    # gnfs process not run
    echo "`date '+%Y-%m-%d %H:%M:%S'`:gnfs process not run, can not get check_nfs_status." >> ${log_file}
  else
    total=$(gnfs_mgr get switch Nfs_Service_Status|grep 'switch value is :'| awk '{print $5}')
    if [ -z "${total}" ];then
      # gnfs is restart now
      echo "`date '+%Y-%m-%d %H:%M:%S'`:gnfs dbus is error, can not get check_nfs_status." >> ${log_file}
    else
      echo "`date '+%Y-%m-%d %H:%M:%S'`: status:${total}"
    fi
  fi
        sleep 5
done
}


